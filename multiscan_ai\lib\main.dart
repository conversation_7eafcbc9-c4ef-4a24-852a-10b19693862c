import 'package:flutter/material.dart';
import 'package:multiscan_ai/services/supabase_client.dart';
import 'package:multiscan_ai/viewmodels/auth_viewmodel.dart';
import 'package:provider/provider.dart';
// Corrected import paths to use relative paths
import '../views/splash_screen.dart';
import '../views/home_screen.dart';
import '../views/login_screen.dart';
import '../views/signup_screen.dart';
// Removed unused import 'package:multiscanai_app/screens/login_screen.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Supabase
  await SupabaseService.initialize();

  runApp(
    MultiProvider(
      providers: [ChangeNotifierProvider(create: (_) => AuthViewModel())],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'MultiScan AI',
      theme: ThemeData(
        // Set primary colors - green is our main brand color
        primarySwatch: Colors.green,
        primaryColor: const Color(0xFF008037), // Deep green for primary actions
        scaffoldBackgroundColor: Colors.white, // Clean white background
        // AppBar styling - clean, minimal look with no elevation
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.white,
          elevation: 0,
          iconTheme: IconThemeData(color: Colors.black),
          titleTextStyle: TextStyle(
            color: Colors.black87,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),

        // Input field styling - clean with subtle borders
        inputDecorationTheme: InputDecorationTheme(
          filled: true,
          fillColor: Colors.white,
          hintStyle: TextStyle(color: Colors.grey[400], fontSize: 16),
          contentPadding: const EdgeInsets.symmetric(
            vertical: 18.0,
            horizontal: 16.0,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.0),
            borderSide: BorderSide(color: Colors.grey[200]!, width: 1),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.0),
            borderSide: BorderSide(color: Colors.grey[200]!, width: 1),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.0),
            borderSide: const BorderSide(color: Color(0xFF2B6DF8), width: 1.5),
          ),
          prefixIconColor: Colors.grey[400],
        ),

        // Button styling - full width with rounded corners
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF008037), // Green buttons
            foregroundColor: Colors.white,
            minimumSize: const Size(
              double.infinity,
              56,
            ), // Full width, taller height
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.0),
            ),
            elevation: 0, // Flat design with no shadows
            textStyle: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),

        // Link styling - blue color for all text buttons
        textButtonTheme: TextButtonThemeData(
          style: TextButton.styleFrom(
            foregroundColor: const Color(0xFF2B6DF8), // Blue for links
            textStyle: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 15,
            ),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          ),
        ),

        // Checkbox styling - square with rounded corners
        checkboxTheme: CheckboxThemeData(
          fillColor: MaterialStateProperty.resolveWith((states) {
            if (states.contains(MaterialState.selected)) {
              return const Color(0xFF2B6DF8); // Blue when checked
            }
            return null;
          }),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
        ),

        // Radio button styling - blue when selected
        radioTheme: RadioThemeData(
          fillColor: MaterialStateProperty.resolveWith((states) {
            if (states.contains(MaterialState.selected)) {
              return const Color(0xFF2B6DF8); // Blue for selected radio buttons
            }
            return null;
          }),
        ),

        // Card styling - subtle elevation with rounded corners
        cardTheme: CardTheme(
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          clipBehavior: Clip.antiAlias,
        ),

        // Bottom navigation bar styling - clean with selected blue items
        bottomNavigationBarTheme: const BottomNavigationBarThemeData(
          backgroundColor: Colors.white,
          selectedItemColor: Color(0xFF2B6DF8), // Blue for selected item
          unselectedItemColor: Colors.grey,
          selectedLabelStyle: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 12,
          ),
          unselectedLabelStyle: TextStyle(fontSize: 12),
          showUnselectedLabels: true,
          type: BottomNavigationBarType.fixed,
          elevation: 8, // Light shadow for the navigation bar
        ),
      ),
      // Use initialRoute and routes based on corrected imports
      initialRoute: '/splash', // Use the string route name
      routes: {
        // Use string route names defined in AppRoutes or directly
        '/splash': (context) => const SplashScreen(),
        '/home': (context) => const HomeScreen(),
        '/login': (context) => const LoginScreen(),
        '/signup': (context) => const SignupScreen(),
        // Consider using the AppRoutes class for consistency if preferred
        // AppRoutes.splash: (context) => const SplashScreen(),
        // AppRoutes.home: (context) => const HomeScreen(),
        // AppRoutes.login: (context) => const LoginScreen(),
        // AppRoutes.signup: (context) => const SignupScreen(),
      },
      // Keep onGenerateRoute for handling potential dynamic routes or errors
      // onGenerateRoute: AppRoutes.generateRoute, // If using AppRoutes
      debugShowCheckedModeBanner: false,
    );
  }
}
