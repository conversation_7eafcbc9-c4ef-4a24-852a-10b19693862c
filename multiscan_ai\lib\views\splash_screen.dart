import 'dart:async'; // Import async library for Timer
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart'; // Import FontAwesome
import '../views/login_screen.dart'; // Keep login screen import
// TODO: Potentially import home screen if needed after auth check
// import '../views/home_screen.dart'; 

// Convert to StatefulWidget for loading logic
class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  static const String routeName = '/splash';

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {

  @override
  void initState() {
    super.initState();
    _startLoadingTimer();
  }

  void _startLoadingTimer() {
    // Simulate a loading period (e.g., 3 seconds)
    // TODO: Replace this Timer with actual data fetching/auth check logic
    Timer(const Duration(seconds: 3), () {
      // After the timer, navigate to the Login Screen
      // In a real app, check auth status and navigate accordingly
      if (mounted) { // Check if the widget is still in the tree
        Navigator.pushReplacementNamed(context, LoginScreen.routeName);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Removed screenSize calculation as it's not needed for centered layout

    // Use the purple background color from the image
    const Color splashBackgroundColor = Color(0xFF6200EE); // Example purple, adjust if needed

    return Scaffold(
      backgroundColor: splashBackgroundColor, // Set background color
      body: Center( // Center the content
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center, // Center vertically
          children: [
            // Flask Icon
            const FaIcon( // Use FontAwesome icon
              FontAwesomeIcons.flask, 
              size: 80, 
              color: Colors.white
            ),
            const SizedBox(height: 24),
            // App Title
            const Text(
              'MultiScan AI',
              style: TextStyle(
                fontSize: 32, 
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            // Subtitle
            const Text(
              'Medical Sample Analysis',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
            ),
            const SizedBox(height: 40),
            // Loading Indicator
            const SizedBox(
              width: 20, // Constrain size of the indicator
              height: 20,
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                strokeWidth: 2.0, // Make the line thinner
              ),
            ),
          ],
        ),
      ),
    );
  }
} 