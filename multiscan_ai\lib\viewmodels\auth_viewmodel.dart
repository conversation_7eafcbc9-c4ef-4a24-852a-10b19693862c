import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import '../services/supabase_client.dart';

class AuthViewModel with ChangeNotifier {
  bool _isLoading = false;
  String? _errorMessage;

  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  Future<AppUser?> signUp({
    required String email,
    required String password,
    required UserRole role,
    String? fullName,
    String? phone,
    bool agreeTerms = false,
    bool agreeMarketing = false,
  }) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      if (!agreeTerms) {
        throw Exception(
          'You must agree to the Terms of Service and Privacy Policy',
        );
      }

      // 1. Sign up with Supabase Auth
      final authResponse = await Supabase.instance.client.auth.signUp(
        email: email,
        password: password,
      );

      if (authResponse.user == null) {
        throw Exception('Sign up failed - no user returned');
      }

      // 2. Create user profile in the database
      final userData = {
        'id': authResponse.user!.id,
        'email': email,
        'full_name': fullName,
        'phone': phone,
        'role': role.toString().split('.').last,
        'agree_marketing': agreeMarketing,
      };

      final response = await Supabase.instance.client
          .from('profiles')
          .insert(userData);

      if (response.error != null) {
        // If profile creation fails, delete the auth user to keep data consistent
        // If profile creation fails, delete the auth user to keep data consistent
        await Supabase.instance.client.auth.admin.deleteUser(
          authResponse.user!.id,
        );
      }

      return AppUser(
        id: authResponse.user!.id,
        email: email,
        fullName: fullName,
        phone: phone,
        role: role,
        createdAt: DateTime.now(),
      );
    } catch (e) {
      _errorMessage = e.toString();
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
