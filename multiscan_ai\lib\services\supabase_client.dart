import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseService {
  static late String supabaseUrl;
  static late String supabaseAnonKey;

  static Future<void> initialize() async {
    try {
      // Load .env file from root of your project
      await dotenv.load(fileName: '.env');

      // Get environment variables
      supabaseUrl = dotenv.get('SUPABASE_URL');
      supabaseAnonKey = dotenv.get('SUPABASE_ANON_KEY');

      await Supabase.initialize(
        url: supabaseUrl,
        anonKey: supabaseAnonKey,
        authOptions: const FlutterAuthClientOptions(
          authFlowType: AuthFlowType.pkce,
        ),
      );
      print('✅ Supabase connected successfully!');
    } catch (e) {
      print('❌ Error connecting to Supabase: $e');
      // Provide fallback values or detailed error message
      print('Make sure you have a .env file in your project root with:');
      print('SUPABASE_URL=your_url_here');
      print('SUPABASE_ANON_KEY=your_key_here');
      rethrow;
    }
  }

  static Supabase get instance => Supabase.instance;
}
