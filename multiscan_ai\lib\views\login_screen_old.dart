import 'package:flutter/material.dart';
import '../viewmodels/login_viewmodel.dart';

class LoginScreen extends StatelessWidget {
  final LoginViewModel _viewModel = LoginViewModel();

  LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Sign In'), centerTitle: true),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Form(
          key: _viewModel.formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Center(
                child: Text(
                  'Multiscan AI',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(height: 10),
              const Center(
                child: Text('Welcome back! Please sign in to continue'),
              ),
              const SizedBox(height: 30),

              // Email/Phone Field
              const Text('Email or Phone'),
              TextFormField(
                decoration: const InputDecoration(
                  hintText: 'Enter email or phone',
                  border: OutlineInputBorder(),
                ),
                validator: _viewModel.validateEmailOrPhone,
                onChanged: _viewModel.updateEmailOrPhone,
              ),
              const SizedBox(height: 20),

              // Password Field
              const Text('Password'),
              TextFormField(
                obscureText: true,
                decoration: const InputDecoration(
                  hintText: 'Enter password',
                  border: OutlineInputBorder(),
                ),
                validator: _viewModel.validatePassword,
                onChanged: _viewModel.updatePassword,
              ),
              const SizedBox(height: 10),

              // Remember me & Forgot password
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Checkbox(
                        value: _viewModel.rememberMe,
                        onChanged:
                            (value) =>
                                _viewModel.toggleRememberMe(value ?? false),
                      ),
                      const Text('Remember me'),
                    ],
                  ),
                  TextButton(
                    onPressed:
                        () => _viewModel.navigateToForgotPassword(context),
                    child: const Text(
                      'Forgot Password?',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 30),

              // Sign In Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => _viewModel.login(context),
                  child: const Text('Sign In'),
                ),
              ),
              const SizedBox(height: 20),

              // Sign Up prompt
              Center(
                child: TextButton(
                  onPressed: () => _viewModel.navigateToSignUp(context),
                  child: const Text("Don't have an account? Sign Up"),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
