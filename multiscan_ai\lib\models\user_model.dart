enum UserRole { healthcareWorker, labTechnician, admin }

class AppUser {
  final String id;
  final String email;
  final String? fullName;
  final String? phone;
  final UserRole role;
  final DateTime? createdAt;

  AppUser({
    required this.id,
    required this.email,
    this.fullName,
    this.phone,
    required this.role,
    this.createdAt,
  });

  factory AppUser.fromJson(Map<String, dynamic> json) {
    return AppUser(
      id: json['id'],
      email: json['email'],
      fullName: json['full_name'],
      phone: json['phone'],
      role: _parseRole(json['role']),
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'full_name': fullName,
      'phone': phone,
      'role': _roleToString(role),
      'created_at': createdAt?.toIso8601String(),
    };
  }

  static UserRole _parseRole(String role) {
    switch (role) {
      case 'healthcareWorker':
        return UserRole.healthcareWorker;
      case 'labTechnician':
        return UserRole.labTechnician;
      case 'admin':
        return UserRole.admin;
      default:
        return UserRole.healthcareWorker;
    }
  }

  static String _roleToString(UserRole role) {
    switch (role) {
      case UserRole.healthcareWorker:
        return 'healthcareWorker';
      case UserRole.labTechnician:
        return 'labTechnician';
      case UserRole.admin:
        return 'admin';
    }
  }
}
