import { useEffect, useState } from "react";
import medicalRecordsData from "../data/medicalRecordsData.json";
import type { MedicalRecordsData } from "../types/dashboard";
import { GoGraph } from "react-icons/go";
import { IoPeople } from "react-icons/io5";
import { FaRegCalendar } from "react-icons/fa";
import { RiSettings3Fill } from "react-icons/ri";
import { BsExclamationCircle } from "react-icons/bs";
import { IoNotificationsOutline } from "react-icons/io5";
import { IoIosArrowDown } from "react-icons/io";
import { IoIosSearch } from "react-icons/io";
import { NavLink } from "react-router-dom";
import { FaMicroscope } from "react-icons/fa";
import { TbCopyPlusFilled } from "react-icons/tb";

const MedicalRecords = () => {
  const [data, setData] = useState<MedicalRecordsData | null>(null);

  useEffect(() => {
    setData(medicalRecordsData as MedicalRecordsData);
  }, []);

  if (!data) return <div>Loading...</div>;

  const patient = data.records[0]; // Displaying Emily Wilson's record as per the design

  return (
    <div className="flex bg-gray-50 h-screen overflow-hidden">
      {/* Sidebar */}
      <aside className="w-64 bg-white flex flex-col h-screen sticky top-0 overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center space-x-3">
            <img
              src="multiscan_logo.png"
              alt="MultiScan AI Logo"
              className="h-10 w-10"
            />
            <span className="text-2xl font-bold text-gray-900">
              MultiScan AI
            </span>
          </div>
        </div>
        <div className="p-6 flex-1">
          <nav className="space-y-4">
            <NavLink
              to="/dashboard"
              className={({ isActive }) =>
                `flex items-center space-x-2 p-2 rounded cursor-pointer ${
                  isActive
                    ? "bg-blue-100 text-blue-600"
                    : "hover:bg-gray-100 text-gray-700"
                }`
              }
            >
              <GoGraph className="size-5" />
              <span className="font-semibold">Dashboard</span>
            </NavLink>
            <NavLink
              to="/patients"
              className={({ isActive }) =>
                `flex items-center space-x-2 p-2 rounded cursor-pointer ${
                  isActive
                    ? "bg-blue-100 text-blue-600"
                    : "hover:bg-gray-100 text-gray-700"
                }`
              }
            >
              <IoPeople className="size-5" />
              <span>Patients</span>
            </NavLink>
            <NavLink
              to="/analysis"
              className={({ isActive }) =>
                `flex items-center space-x-2 p-2 rounded cursor-pointer ${
                  isActive
                    ? "bg-blue-100 text-blue-600"
                    : "hover:bg-gray-100 text-gray-700"
                }`
              }
            >
              <FaRegCalendar className="size-5" />
              <span>Analysis</span>
            </NavLink>
            <NavLink
              to="/medical-records"
              className={({ isActive }) =>
                `flex items-center space-x-2 p-2 rounded cursor-pointer ${
                  isActive
                    ? "bg-blue-100 text-blue-600"
                    : "hover:bg-gray-100 text-gray-700"
                }`
              }
            >
              <TbCopyPlusFilled className="size-5" />
              <span>Medical Records</span>
            </NavLink>
            <NavLink
              to="/settings"
              className={({ isActive }) =>
                `flex items-center space-x-2 p-2 rounded cursor-pointer ${
                  isActive
                    ? "bg-blue-100 text-blue-600"
                    : "hover:bg-gray-100 text-gray-700"
                }`
              }
            >
              <RiSettings3Fill className="size-5" />
              <span>Settings</span>
            </NavLink>
          </nav>
        </div>
        <div className="p-6 space-y-4">
          <div className="flex items-center space-x-2 p-2 text-gray-500 hover:bg-gray-100 rounded cursor-pointer">
            <BsExclamationCircle className="size-5" />
            <span className="font-medium">Help Centre</span>
          </div>
          <button className="w-full py-2 cursor-pointer bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            Log Out
          </button>
        </div>
      </aside>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Nav Bar */}
        <div className="flex bg-white p-4 h-16 sticky top-0 z-10">
          <div className="flex items-center border border-gray-300 rounded-lg px-3 py-1 focus-within:ring-2 focus-within:ring-blue-500 mr-6 flex-grow">
            <IoIosSearch className="text-gray-400 mr-2 size-6" />
            <input
              type="text"
              placeholder="Search for anything..."
              className="outline-none w-full"
            />
          </div>
          <div className="flex items-center space-x-4 mr-4">
            <IoNotificationsOutline className="size-5 text-gray-600 cursor-pointer" />
            <div className="flex items-center ml-auto space-x-2">
              <img
                src="avatar.png"
                alt="User Avatar"
                className="w-10 h-10 rounded-full"
              />
              <span className="text-gray-700">Flore</span>
              <IoIosArrowDown className="text-gray-600 size-4 cursor-pointer" />
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <main className="flex-1 p-8 overflow-y-auto">
          <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
            <div className="flex items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mr-6">
                Patient Record
              </h2>
            </div>

            {/* Patient Header */}
            <div className="flex items-center mb-6">
              <img
                src={`/${patient.avatar}`}
                alt={patient.name}
                className="w-16 h-16 rounded-full mr-4"
              />
              <div>
                <h3 className="text-xl font-bold text-gray-900">
                  {patient.name}
                </h3>
                <div className="text-gray-500">
                  {patient.age} years old, {patient.gender} | Patient ID:{" "}
                  {patient.id}
                  <span className="ml-2 bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs">
                    {patient.condition}
                  </span>
                </div>
              </div>
              <div className="ml-auto flex space-x-2">
                <button className="px-4 cursor-pointer py-1 font-bold border border-gray-500 rounded-lg text-gray-700 hover:bg-gray-100">
                  Edit
                </button>
                <button className="px-4 cursor-pointer py-1 font-bold bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                  New Scan
                </button>
              </div>
            </div>

            {/* Tabs */}
            <div className="flex border-b border-gray-200 mb-6">
              <button className="px-4 py-2 cursor-pointer text-blue-600 border-b-2 border-blue-600 font-semibold">
                Overview
              </button>
              <button className="px-4 py-2 cursor-pointer text-gray-500 hover:text-blue-600">
                Scans
              </button>
              <button className="px-4 py-2 cursor-pointer text-gray-500 hover:text-blue-600">
                Medical History
              </button>
            </div>

            {/* Content Area */}
            <div className="flex">
              {/* Recent Scan Results */}
              <div className="flex-1 mr-6 bg-gray-100 rounded-md p-5">
                <div className="flex items-center mb-4 border-b border-gray-200 pb-2">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Recent Scan Results
                  </h3>
                  <button className="px-4 py-1 cursor-pointer bg-white font-bold border border-gray-500 rounded-lg text-gray-700 hover:bg-gray-100 ml-auto">
                    View All
                  </button>
                </div>
                <div className="space-y-4">
                  {patient.scans.map((scan, index) => (
                    <div
                      key={index}
                      className="flex items-start p-4 rounded-lg border-b border-gray-200"
                    >
                      <div
                        className={`flex items-center justify-center size-10 mr-4 rounded-lg ${scan.iconColor}`}
                      >
                        <FaMicroscope className="size-5 text-white" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className="font-semibold text-gray-900">
                            {scan.type}
                          </h4>
                          <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
                            {scan.result}
                          </span>
                        </div>
                        <div className="text-gray-500 text-sm">
                          {scan.doctor} • {scan.date}
                        </div>
                        <p className="text-gray-600 mt-1">{scan.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Patient Information */}
              <div className="w-80 bg-gray-100 p-5 rounded-md h-70">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Patient Information
                  </h3>
                  <button className="px-4 py-1 bg-white cursor-pointer font-bold border border-gray-500 rounded-lg text-gray-700 hover:bg-gray-100">
                    Edit
                  </button>
                </div>
                <div className="space-y-4">
                  <div className="flex border-b border-gray-200 pb-1">
                    <span className="block text-sm text-gray-500">Email</span>
                    <span className="ml-auto font-bold">
                      {patient.patientInfo.email}
                    </span>
                  </div>
                  <div className="flex border-b border-gray-200 pb-1">
                    <span className="block text-sm text-gray-500">Phone</span>
                    <span className="ml-auto font-bold">
                      {patient.patientInfo.phone}
                    </span>
                  </div>
                  <div className="flex border-b border-gray-200 pb-1">
                    <span className="block text-sm text-gray-500">
                      Date of Birth
                    </span>
                    <span className="ml-auto font-bold">
                      {patient.patientInfo.dob}
                    </span>
                  </div>
                  <div className="flex border-b border-gray-200 pb-1">
                    <span className="block text-sm text-gray-500">
                      Blood Type
                    </span>
                    <span className="ml-auto font-bold">
                      {patient.patientInfo.bloodType}
                    </span>
                  </div>
                  <div className="flex pb-1">
                    <span className="block text-sm text-gray-500">
                      Primary Doctor
                    </span>
                    <span className="ml-auto font-bold">
                      {patient.patientInfo.primaryDoctor}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default MedicalRecords;
