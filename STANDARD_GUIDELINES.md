
# 🧑‍💻 Contributing to Multiscan AI

Welcome to the Multiscan AI project! This document outlines our **coding standards**, **branching strategy**, and best practices for contributing effectively.

---

## 📂 Project Structure Overview

```
multiscan_ai/
├── lib/
│   ├── core/           # Constants, Services, Utilities
│   ├── features/       # Feature-based folders (auth, dashboard, etc.)
│   ├── widgets/        # Reusable shared widgets
├── test/               # Unit tests
├── integration_test/   # Integration/UI flow tests
```

---

## 🧠 Coding Guidelines

### 🔤 Naming Conventions

| Type         | Format           | Example                      |
|--------------|------------------|------------------------------|
| Class        | PascalCase       | `LoginScreen`, `AuthViewModel` |
| Variables    | camelCase        | `authService`, `scanResult` |
| Constants    | UPPER_SNAKE_CASE | `MAX_RETRY_ATTEMPTS`        |
| Files        | snake_case       | `login_screen.dart`         |

---

### 🧼 Clean Code Practices

- Use `StatelessWidget` unless state is required.
- Avoid business logic in UI. Use `ViewModel` for logic.
- Use `const` constructors wherever possible.
- Reuse UI components via `widgets/` directory.
- Keep widget `build()` methods small (<150 lines ideally).
- Use central `strings.dart` for all display texts.
- Avoid print/debug logs in production code.

---

## 🚀 Git Workflow

### 🔧 Branch Naming

| Branch Type | Prefix        | Example                          |
|-------------|---------------|----------------------------------|
| Feature     | `feature/`    | `feature/scan-ui`                |
| Bug Fix     | `bugfix/`     | `bugfix/fix-login-crash`         |
| Refactor    | `refactor/`   | `refactor/api-service`          |
| Hotfix      | `hotfix/`     | `hotfix/fix-critical-crash`     |
| Test        | `test/`       | `test/add-auth-tests`           |
| Chore       | `chore/`      | `chore/update-dependencies`     |

---

### ✅ Git Process

```bash
# 1. Checkout main and pull latest
git checkout main
git pull origin main

# 2. Create feature branch
git checkout -b feature/your-task-name

# 3. Work and commit
git add .
git commit -m "feat(module): add your change here"

# 4. Push branch
git push origin feature/your-task-name

# 5. Open a Pull Request (PR) to `main`
```

---

## 💬 Commit Message Guidelines (Conventional Commits)

```
<type>(scope): short description

# Examples:
feat(auth): implement user login flow
fix(scan): resolve null data crash
refactor(api): clean up redundant code
```

**Types:** feat, fix, chore, docs, style, refactor, test, perf

---

## 🔍 Code Review Checklist (PR)

- [ ] Descriptive PR title and body
- [ ] Linked to related issue (if available)
- [ ] No commented-out code or console logs
- [ ] Follows architecture & naming conventions
- [ ] Passed tests (`flutter test`)
- [ ] Screens tested manually if applicable

---

## 🧪 Testing

### Unit Tests
- Place in `test/viewmodels/` and `test/mock_services/`.
- Use `mockito` or `mocktail` for service mocking.

### Integration Tests
- Add end-to-end tests in `integration_test/`.

Run tests with:
```bash
flutter test
flutter drive --target=integration_test/app_test.dart
```

---

## 🛠️ Recommended Tools

- **Linting**: Use `flutter_lints` package
- **Analyzer**: Add `analysis_options.yaml`
- **Formatter**: Run `dart format .` before commits

---

## 🤝 Thanks for Contributing!

We appreciate your help in building a high-quality Flutter app. For questions or suggestions, feel free to create an issue or start a discussion in the repo.

Happy coding! 💙
