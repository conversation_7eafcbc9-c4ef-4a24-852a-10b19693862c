export interface DashboardStats {
  totalPatients: number;
  totalPatientsChange: number;
  todaysReports: number;
  urgentReports: number;
  newPatients: number;
  newPatientsChange: number;
  criticalCases: number;
  criticalCasesUrgent: number;
}

export interface MisdiagnosisCase {
  month: string;
  offline: number;
  online: number;
}

export interface MisdiagnosisRegion {
  region: string;
  lat: number;
  lng: number;
  cases: number;
}

export interface RecentPatient {
  id: string;
  name: string;
  avatar: string;
  age: number;
  contact: string;
  email: string;
  condition: string;
  lastVisit: string;
}

export interface ScanResult {
  type: string;
  doctor: string;
  date: string;
  result: string;
  description: string;
  iconColor: string;
}

export interface PatientInfo {
  email: string;
  phone: string;
  dob: string;
  bloodType: string;
  primaryDoctor: string;
}

export interface MedicalRecord {
  id: string;
  name: string;
  avatar: string;
  age: number;
  gender: string;
  condition: string;
  scans: ScanResult[];
  patientInfo: PatientInfo;
}

export interface DashboardData {
  stats: DashboardStats;
  misdiagnosisCases: MisdiagnosisCase[];
  misdiagnosisRegions: MisdiagnosisRegion[];
  recentPatients: RecentPatient[];
}

export interface MedicalRecordsData {
  records: MedicalRecord[];
}
