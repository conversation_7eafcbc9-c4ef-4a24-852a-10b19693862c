import { useEffect, useState } from "react";
import patientsData from "../data/patientsData.json";
import type { RecentPatient } from "../types/dashboard";
import { GoGraph } from "react-icons/go";
import { IoPeople } from "react-icons/io5";
import { FaRegCalendar, FaUserPlus } from "react-icons/fa";
import { TbCopyPlusFilled } from "react-icons/tb";
import { RiSettings3Fill } from "react-icons/ri";
import { BsExclamationCircle } from "react-icons/bs";
import { IoNotificationsOutline } from "react-icons/io5";
import { IoIosArrowDown } from "react-icons/io";
import { PiFilePlusFill } from "react-icons/pi";
import { IoIosSearch } from "react-icons/io";
import { FiFilter } from "react-icons/fi";

import { NavLink } from "react-router-dom";

const Patients = () => {
  const [data, setData] = useState<RecentPatient[] | null>(null);

  useEffect(() => {
    setData(patientsData.patients as RecentPatient[]);
  }, []);

  if (!data) return <div>Loading...</div>;

  return (
    <div className="flex bg-gray-50 h-screen overflow-hidden">
      {/* Sidebar */}
      <aside className="w-64 bg-white flex flex-col h-screen sticky top-0 overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center space-x-3">
            <img
              src="multiscan_logo.png"
              alt="MultiScan AI Logo"
              className="h-10 w-10"
            />
            <span className="text-2xl font-bold text-gray-900">
              MultiScan AI
            </span>
          </div>
        </div>
        <div className="p-6 flex-1">
          <nav className="space-y-4">
            <NavLink
              to="/dashboard"
              className={({ isActive }) =>
                `flex items-center space-x-2 p-2 rounded cursor-pointer ${
                  isActive
                    ? "bg-blue-100 text-blue-600"
                    : "hover:bg-gray-100 text-gray-700"
                }`
              }
            >
              <GoGraph className="size-5" />
              <span className="font-semibold">Dashboard</span>
            </NavLink>
            <NavLink
              to="/patients"
              className={({ isActive }) =>
                `flex items-center space-x-2 p-2 rounded cursor-pointer ${
                  isActive
                    ? "bg-blue-100 text-blue-600"
                    : "hover:bg-gray-100 text-gray-700"
                }`
              }
            >
              <IoPeople className="size-5" />
              <span>Patients</span>
            </NavLink>
            <NavLink
              to="/analysis"
              className={({ isActive }) =>
                `flex items-center space-x-2 p-2 rounded cursor-pointer ${
                  isActive
                    ? "bg-blue-100 text-blue-600"
                    : "hover:bg-gray-100 text-gray-700"
                }`
              }
            >
              <FaRegCalendar className="size-5" />
              <span>Analysis</span>
            </NavLink>
            <NavLink
              to="/medical-records"
              className={({ isActive }) =>
                `flex items-center space-x-2 p-2 rounded cursor-pointer ${
                  isActive
                    ? "bg-blue-100 text-blue-600"
                    : "hover:bg-gray-100 text-gray-700"
                }`
              }
            >
              <TbCopyPlusFilled className="size-5" />
              <span>Medical Records</span>
            </NavLink>
            <NavLink
              to="/settings"
              className={({ isActive }) =>
                `flex items-center space-x-2 p-2 rounded cursor-pointer ${
                  isActive
                    ? "bg-blue-100 text-blue-600"
                    : "hover:bg-gray-100 text-gray-700"
                }`
              }
            >
              <RiSettings3Fill className="size-5" />
              <span>Settings</span>
            </NavLink>
          </nav>
        </div>
        <div className="p-6 space-y-4">
          <div className="flex items-center space-x-2 p-2 text-gray-500 hover:bg-gray-100 rounded cursor-pointer">
            <BsExclamationCircle className="size-5" />
            <span className="font-medium">Help Centre</span>
          </div>
          <button className="w-full py-2 cursor-pointer bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            Log Out
          </button>
        </div>
      </aside>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Nav Bar */}
        <div className="flex bg-white p-4 h-16 sticky top-0 z-10">
          <div className="flex items-center border border-gray-300 rounded-lg px-3 py-1 focus-within:ring-2 focus-within:ring-blue-500 mr-6 flex-grow">
            <IoIosSearch className="text-gray-400 mr-2 size-6" />
            <input
              type="text"
              placeholder="Search for anything..."
              className="outline-none w-full"
            />
          </div>
          <div className="flex items-center space-x-4 mr-4">
            <IoNotificationsOutline className="size-5 text-gray-600 cursor-pointer" />
            <div className="flex items-center ml-auto space-x-2">
              <img
                src="avatar.png"
                alt="User Avatar"
                className="w-10 h-10 rounded-full"
              />
              <span className="text-gray-700">Flore</span>
              <IoIosArrowDown className="text-gray-600 size-4 cursor-pointer" />
            </div>
          </div>
        </div>

        <main className="flex-1 p-8 overflow-y-auto">
          <div className="flex items-center mb-4 p-2 bg-white">
            <h2 className="text-xl font-bold text-gray-900 mr-6">
              Patients List
            </h2>
            <div className="flex items-center border border-gray-300 rounded-lg px-3 py-1 focus-within:ring-2 focus-within:ring-blue-500 ml-auto mr-4">
              <IoIosSearch className="text-gray-400 mr-2 size-6" />
              <input
                type="text"
                placeholder="Search patients..."
                className="outline-none w-full"
              />
            </div>
            {/* <input
              type="text"
              placeholder="Search patients..."
              className="px-4 py-1 mr-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ml-auto"
            /> */}
            <button className="flex items-center px-2 py-2 mr-4 ml-14 cursor-pointer border border-gray-200 rounded-md text-gray-400 hover:bg-gray-50">
              <FiFilter className="mr-2 text-xl" />
              <span className="text-gray-500 font-medium">Filter</span>
              <IoIosArrowDown className="ml-2 text-gray-600 text-sm" />
            </button>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white">
              <thead>
                <tr className="bg-gray-100">
                  <th className="py-3 px-4 text-left text-gray-700 font-semibold">
                    Patient ID
                  </th>
                  <th className="py-3 px-4 text-left text-gray-700 font-semibold">
                    Patient
                  </th>
                  <th className="py-3 px-4 text-left text-gray-700 font-semibold">
                    Age
                  </th>
                  <th className="py-3 px-4 text-left text-gray-700 font-semibold">
                    Contact
                  </th>
                  <th className="py-3 px-4 text-left text-gray-700 font-semibold">
                    Condition
                  </th>
                  <th className="py-3 px-4 text-left text-gray-700 font-semibold">
                    Last Visit
                  </th>
                  <th className="py-3 px-4 text-left text-gray-700 font-semibold">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {data.map((patient) => (
                  <tr key={patient.id} className="hover:bg-gray-50">
                    <td className="py-3 px-4">{patient.id}</td>
                    <td className="py-3 px-4 flex items-center space-x-2">
                      <img
                        src={`/${patient.avatar}`}
                        alt={patient.name}
                        className="w-8 h-8 rounded-full"
                      />
                      <span>
                        {patient.name}{" "}
                        <span className="text-gray-500 text-sm">
                          #{patient.id.slice(-4)}
                        </span>
                      </span>
                    </td>
                    <td className="py-3 px-4">{patient.age}</td>
                    <td className="py-3 px-4">
                      {patient.contact}
                      <br />
                      <span className="text-xs text-gray-400">
                        {patient.email}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs">
                        {patient.condition}
                      </span>
                    </td>
                    <td className="py-3 px-4">{patient.lastVisit}</td>
                    <td className="py-3 px-4 text-blue-600 cursor-pointer">
                      View Details
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Patients;
