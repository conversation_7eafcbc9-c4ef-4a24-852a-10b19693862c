import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

// Define a simple Patient class (can be moved to models later)
class Patient {
  final String id;
  final String name;
  final int age;
  final String gender;

  Patient({required this.id, required this.name, required this.age, required this.gender});
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  static const String routeName = '/home'; // Define a route name

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0; // For Bottom Navigation Bar
  String? _selectedSampleType; // Track selected sample type (e.g., 'Blood', 'Urine')
  Patient? _currentPatient; // Holds patient details after search/creation

  // Controllers for modal forms
  final TextEditingController _patientIdSearchController = TextEditingController();
  final TextEditingController _newPatientIdController = TextEditingController();
  final TextEditingController _newPatientNameController = TextEditingController();
  final TextEditingController _newPatientAgeController = TextEditingController();
  final TextEditingController _newPatientGenderController = TextEditingController();
  final _newPatientFormKey = GlobalKey<FormState>(); // Key for the new patient form
  String? _searchErrorMessage; // State variable for search errors

  @override
  void dispose() {
    // Dispose controllers
    _patientIdSearchController.dispose();
    _newPatientIdController.dispose();
    _newPatientNameController.dispose();
    _newPatientAgeController.dispose();
    _newPatientGenderController.dispose();
    super.dispose();
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
    // Handle navigation for bottom bar items here if needed
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(''), // Empty title
        actions: [ // Add actions list for the top-right icon
          IconButton(
            icon: const Icon(Icons.notifications_none, color: Colors.black87), // Alerts icon
            onPressed: () {
              // TODO: Implement alerts navigation/action
            },
          ),
        ],
        backgroundColor: Colors.white,
        elevation: 0,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60.0),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Search samples or tests...',
                prefixIcon: const Icon(Icons.search, color: Colors.grey),
                filled: true,
                fillColor: Colors.grey[100],
                contentPadding: const EdgeInsets.symmetric(vertical: 0),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                  borderSide: BorderSide.none,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                  borderSide: BorderSide.none,
                ),
              ),
            ),
          ),
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildSectionTitle('Select Sample Type'),
          const SizedBox(height: 16),
          _buildSampleTypeGrid(),
          const SizedBox(height: 24),
          _buildSectionTitle('Quick Links'),
          const SizedBox(height: 16),
          _buildQuickLinks(),
          const SizedBox(height: 24),
          _buildSectionTitle('Recent Scans'),
          const SizedBox(height: 16),
          _buildRecentScans(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        items: const <BottomNavigationBarItem>[
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.science),
            label: 'Tests',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person_outline),
            label: 'Profile',
          ),
        ],
        currentIndex: _selectedIndex,
        selectedItemColor: Colors.blue,
        unselectedItemColor: Colors.grey,
        type: BottomNavigationBarType.fixed,
        showUnselectedLabels: true,
        backgroundColor: Colors.white,
        elevation: 8,
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18, 
        fontWeight: FontWeight.bold, 
        color: Colors.black87
      ),
    );
  }

  Widget _buildSampleTypeGrid() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.3, // Make cards a bit wider than tall
      children: [
        _buildSampleCard('Blood', Icons.water_drop, Colors.red),
        _buildSampleCard('Urine', FontAwesomeIcons.flask, Colors.amber),
        _buildSampleCard('Stool', FontAwesomeIcons.vial, Colors.brown[600]!),
        _buildSampleCard('Sputum', FontAwesomeIcons.lungs, Colors.blue[600]!),
      ],
    );
  }

  Widget _buildSampleCard(String title, IconData icon, Color color) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      color: color,
      child: InkWell(
        onTap: () {
          // Show the first popup modal when a sample type is tapped
          _showPatientIdPromptModal(title);
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 36, color: Colors.white),
              const SizedBox(height: 8),
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickLinks() {
  return Card(
    elevation: 1,
    margin: EdgeInsets.zero,
    color: const Color(0xFFF5F5FA), // Light lavender background
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    child: Column(
      children: [
        _buildQuickLinkItem(FontAwesomeIcons.notesMedical, 'View Reports', () { /* TODO */ }),
      ],
    ),
  );
}

  Widget _buildQuickLinkItem(IconData icon, String title, VoidCallback onTap) {
  return ListTile(
    contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
    leading: Container(
      padding: const EdgeInsets.all(8),
      child: Icon(icon, color: Colors.blue[700], size: 24),
    ),
    title: Text(
      title, 
      style: const TextStyle(
        fontWeight: FontWeight.w500,
        fontSize: 16,
      )
    ),
    trailing: const Icon(Icons.chevron_right, color: Colors.grey),
    onTap: onTap,
  );
}

  Widget _buildRecentScans() {
    return Row(
      children: [
        _buildRecentScanIcon(Icons.water_drop, Colors.red),
        const SizedBox(width: 12),
        _buildRecentScanIcon(FontAwesomeIcons.flask, Colors.amber),
        const SizedBox(width: 12),
        _buildRecentScanIcon(FontAwesomeIcons.lungs, Colors.blue),
      ],
    );
  }

  Widget _buildRecentScanIcon(IconData icon, Color color) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Container(
        width: 50,
        height: 50,
        alignment: Alignment.center,
        child: Icon(icon, color: color),
      ),
    );
  }

  // --- Modal Display Functions ---

  // Shows the first modal: Enter Patient ID or Create New
  void _showPatientIdPromptModal(String sampleType) {
    setState(() {
      _selectedSampleType = sampleType; // Store the selected type
      _patientIdSearchController.clear(); // Clear previous search term
      _searchErrorMessage = null; // Clear any previous error message
    });

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent, // Make background transparent
      builder: (_) => _buildPatientIdPromptModalContent(),
    );
  }

  // Shows the 'New Patient' modal
  void _showNewPatientModal() {
    // Clear controllers before showing
    _newPatientIdController.clear();
    _newPatientNameController.clear();
    _newPatientAgeController.clear();
    _newPatientGenderController.clear();

    Navigator.pop(context); // Close the previous modal (Patient ID prompt)

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (_) => _buildNewPatientModalContent(),
    );
  }

  // Shows the 'Patient Profile' modal
  void _showPatientProfileModal(Patient patient) {
    setState(() {
      _currentPatient = patient; // Ensure current patient is set
    });
    Navigator.pop(context); // Close previous modal (Patient ID or New Patient)
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (_) => _buildPatientProfileModalContent(patient),
    );
  }

  // Shows the 'Scan Method' selection modal
  void _showScanMethodModal() {
    Navigator.pop(context); // Close the profile modal
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (_) => _buildScanMethodModalContent(),
    );
  }

  // --- Modal Content Builders ---

  // Builds the content for the first modal (Patient ID Prompt)
  Widget _buildPatientIdPromptModalContent() {
    // Use StatefulBuilder to allow updating the error message within the modal
    return StatefulBuilder(
      builder: (BuildContext context, StateSetter setModalState) {
        return Container(
          padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom), // Adjust for keyboard
          decoration: const BoxDecoration(
             color: Colors.white,
             borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
             ),
          ),
          child: Padding(
             padding: const EdgeInsets.all(24.0),
             child: Column(
                mainAxisSize: MainAxisSize.min, // Take only needed height
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                   Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                         const Text(
                            'Patient ID',
                            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                         ),
                         IconButton(
                            icon: const Icon(Icons.close),
                            onPressed: () => Navigator.pop(context),
                         ),
                      ],
                   ),
                   const SizedBox(height: 8),
                   if (_selectedSampleType != null)
                     Text(
                       'Sample Type: $_selectedSampleType Sample',
                       style: const TextStyle(
                         fontSize: 16,
                         fontWeight: FontWeight.w500,
                         color: Colors.red,
                       ),
                     ),
                   const SizedBox(height: 20),
                   TextField(
                     controller: _patientIdSearchController,
                     decoration: const InputDecoration(
                       hintText: 'Enter Patient ID',
                       prefixIcon: Icon(Icons.search),
                     ),
                   ),
                   // Display error message conditionally
                   if (_searchErrorMessage != null && _searchErrorMessage!.isNotEmpty)
                     Padding(
                       padding: const EdgeInsets.only(top: 8.0),
                       child: Text(
                         _searchErrorMessage!,
                         style: const TextStyle(color: Colors.red, fontSize: 14),
                       ),
                     ),
                   const SizedBox(height: 20),
                   Row(
                      children: [
                         Expanded(
                            child: ElevatedButton(
                               // Pass setModalState to the handler if needed for internal updates
                               onPressed: () => _handleSearchPatient(setModalState),
                               style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF2B6DF8), // Blue Search button
                               ),
                               child: const Text('Search'),
                            ),
                         ),
                         const SizedBox(width: 16),
                         Expanded(
                            child: ElevatedButton(
                               onPressed: _showNewPatientModal,
                               style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF008037), // Green Create button (from theme)
                               ),
                               child: const Text('Create New'),
                            ),
                         ),
                      ],
                   ),
                ],
             ),
          ),
        );
      }
    );
  }

  // Builds the content for the 'New Patient' modal
  Widget _buildNewPatientModalContent() {
    return Container(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom + 20,
        top: 20,
        left: 24,
        right: 24,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Form(
        key: _newPatientFormKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'New Patient',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (_selectedSampleType != null)
              Text(
                'Sample Type: $_selectedSampleType Sample',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.red,
                ),
              ),
            const SizedBox(height: 20),
            // Use specified solid icons with darker color
            TextFormField(
              controller: _newPatientIdController,
              decoration: InputDecoration(labelText: 'Patient ID', prefixIcon: Icon(Icons.badge, color: Colors.grey[700])), // Darker icon
              validator: (value) {
                if (value == null || value.isEmpty) return 'Please enter Patient ID';
                return null;
              },
            ),
            const SizedBox(height: 15),
            TextFormField(
              controller: _newPatientNameController,
              decoration: InputDecoration(labelText: 'Full Name', prefixIcon: Icon(Icons.person, color: Colors.grey[700])), // Darker icon
              validator: (value) {
                if (value == null || value.isEmpty) return 'Please enter Full Name';
                return null;
              },
            ),
            const SizedBox(height: 15),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _newPatientAgeController,
                    decoration: InputDecoration(labelText: 'Age', prefixIcon: Icon(Icons.calendar_today, color: Colors.grey[700])), // Darker icon
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) return 'Please enter Age';
                      if (int.tryParse(value) == null) return 'Invalid Age';
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _newPatientGenderController,
                    decoration: InputDecoration(labelText: 'Gender', prefixIcon: Icon(Icons.people, color: Colors.grey[700])), // Darker icon
                    validator: (value) {
                      if (value == null || value.isEmpty) return 'Please enter Gender';
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 30),
            ElevatedButton(
              onPressed: _handleSaveNewPatient,
              // Button uses the theme style (green)
              child: const Text('Save & Continue'),
            ),
          ],
        ),
      ),
    );
  }

  // Builds the content for the 'Patient Profile' modal
  Widget _buildPatientProfileModalContent(Patient patient) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 24),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Patient Profile',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          const SizedBox(height: 20),
          // Use refactored profile detail row
          _buildProfileDetailRow(Icons.badge, 'ID', patient.id),
          const SizedBox(height: 15),
          _buildProfileDetailRow(Icons.person, 'Name', patient.name),
          const SizedBox(height: 15),
          _buildProfileDetailRow(Icons.calendar_today, 'Age', patient.age.toString()),
          const SizedBox(height: 15),
          _buildProfileDetailRow(Icons.people, 'Gender', patient.gender),
          const SizedBox(height: 25),
          if (_selectedSampleType != null)
            Text(
              'Current Sample: $_selectedSampleType Sample',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.red,
              ),
            ),
          const SizedBox(height: 30),
          ElevatedButton(
            onPressed: _handleProfileContinue,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2B6DF8), // Blue Continue button
            ),
            child: const Text('Continue'),
          ),
          const SizedBox(height: 20), // Add extra padding at the bottom
        ],
      ),
    );
  }

  // Builds the content for the 'Select Scan Method' modal
  Widget _buildScanMethodModalContent() {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Padding(
            padding: EdgeInsets.all(20.0),
            child: Text(
              'Select Scan Method',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ),
          // Divider removed to match screenshot more closely
          ListTile(
            leading: Icon(Icons.remove_red_eye_outlined, color: Colors.blue[700]),
            title: const Text('View from Microscope'),
            onTap: _handleViewMicroscope,
          ),
          ListTile(
            leading: Icon(Icons.upload_file_outlined, color: Colors.green[700]),
            title: const Text('Upload Cell Scan'),
            onTap: _handleUploadScan,
          ),
          const SizedBox(height: 20), // Add extra padding at the bottom
        ],
      ),
    );
  }

  // REFACTORED: Helper widget for profile details row (Label above Value)
  Widget _buildProfileDetailRow(IconData icon, String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start, // Align icon with top text
      children: [
        Icon(icon, color: Colors.blue[700], size: 22), // Blue icon, slightly larger
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(fontSize: 14, color: Colors.grey[600]), // Smaller label
              ),
              const SizedBox(height: 2), // Small space between label and value
              Text(
                value,
                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                // overflow: TextOverflow.ellipsis, // Optional: if value can be long
              ),
            ],
          ),
        ),
      ],
    );
  }

  // --- Logic Handlers ---

  // MODIFIED: Simulate patient found/not found, update state, don't pop on error
  void _handleSearchPatient(StateSetter setModalState) { // Accept setModalState
    String searchId = _patientIdSearchController.text.trim();
    print("Searching for patient ID: $searchId");

    // Clear previous error message before new search
    setModalState(() {
      _searchErrorMessage = null;
    });

    if (searchId.isEmpty) {
      setModalState(() { // Update error state within the modal
        _searchErrorMessage = 'Please enter a Patient ID to search';
      });
      return; // Stop execution
    }

    // --- Simulation Logic ---
    const String existingPatientId = "12345";
    Patient? foundPatient;

    if (searchId == existingPatientId) {
      foundPatient = Patient(id: searchId, name: "Moussa Found", age: 12, gender: "male");
    }
    // --- End Simulation Logic ---

    if (foundPatient != null) {
      Navigator.pop(context); // Close modal ONLY if found
      _showPatientProfileModal(foundPatient);
    } else {
      // Update error state within the modal if not found
      setModalState(() {
        _searchErrorMessage = 'Patient with ID \'$searchId\' not found.';
      });
    }
  }

  // Handles saving the new patient data
  void _handleSaveNewPatient() {
    if (_newPatientFormKey.currentState?.validate() ?? false) {
      final newPatient = Patient(
        id: _newPatientIdController.text,
        name: _newPatientNameController.text,
        age: int.parse(_newPatientAgeController.text), // Already validated as int
        gender: _newPatientGenderController.text,
      );
      setState(() {
        _currentPatient = newPatient; // Store the newly created patient
      });
      _showPatientProfileModal(newPatient); // Proceed to profile modal
    }
  }

  // Handles the 'Continue' button press from the profile modal
  void _handleProfileContinue() {
    print("Continue pressed from profile");
    _showScanMethodModal();
  }

  // Handles selecting 'View from Microscope'
  void _handleViewMicroscope() {
    print("Selected View from Microscope");
    Navigator.pop(context); // Close the modal
    // TODO: Implement navigation or action for microscope view
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Microscope View - Not Implemented Yet')),
    );
  }

  // Handles selecting 'Upload Cell Scan'
  void _handleUploadScan() {
    print("Selected Upload Cell Scan");
    Navigator.pop(context); // Close the modal
    // TODO: Implement navigation or action for upload
     ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Upload Scan - Not Implemented Yet')),
    );
  }

  // Helper to get color based on sample type (from old code)
  Color _getSampleColor(String type) {
    switch (type.toLowerCase()) {
      case 'blood':
        return Colors.red;
      case 'urine':
        return Colors.amber;
      case 'stool':
        return Colors.brown;
      case 'sputum':
        return Colors.blue[600]!;
      default:
        return Colors.grey;
    }
  }
}