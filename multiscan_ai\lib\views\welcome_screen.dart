import 'package:flutter/material.dart';
import '../viewmodels/welcome_viewmodel.dart';

class WelcomeScreen extends StatelessWidget {
  const WelcomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final viewModel = WelcomeViewModel();

    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'Welcome',
              style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            const Text(
              "We're glad you're here",
              style: TextStyle(fontSize: 18),
            ),
            const Sized<PERSON>ox(height: 40),
            ElevatedButton(
              onPressed: () => viewModel.onGetStartedPressed(context),
              child: const Text('Get Started'),
            ),
          ],
        ),
      ),
    );
  }
}
