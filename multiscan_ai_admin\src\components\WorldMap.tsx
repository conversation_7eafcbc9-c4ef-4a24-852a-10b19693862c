import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import type { MisdiagnosisRegion } from "../types/dashboard";
import L from "leaflet";

interface WorldMapProps {
  regions: MisdiagnosisRegion[];
}

const WorldMap = ({ regions }: WorldMapProps) => {
  // Dynamically create icons with case numbers
  const createCustomIcon = (cases: number) =>
    new L.DivIcon({
      className: "custom-div-icon",
      html: `
        <div style="background-color: #FF5533; width: 8px; height: 8px; border-radius: 50%; border: 1px solid #fff;"></div>
        <div style="position: absolute; top: -12px; left: 50%; transform: translateX(-50%); color: #5D5A6D; font-size: 8px; font-family: system-ui;">
          ${cases.toLocaleString()}
        </div>
      `,
      iconSize: [8, 8],
      iconAnchor: [4, 4],
    });

  return (
    <MapContainer
      center={[20, 0]}
      zoom={2}
      style={{ width: "100%", height: "100%" }}
    >
      <TileLayer
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        attribution='© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
      />
      {regions.map((region) => (
        <Marker
          key={region.region}
          position={[region.lat, region.lng]}
          icon={createCustomIcon(region.cases)}
        >
          <Popup>{`${
            region.region
          }: ${region.cases.toLocaleString()} cases`}</Popup>
        </Marker>
      ))}
    </MapContainer>
  );
};

export default WorldMap;
