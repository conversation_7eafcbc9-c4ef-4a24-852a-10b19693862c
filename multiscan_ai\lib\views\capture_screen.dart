import 'package:flutter/material.dart';
import 'package:camera/camera.dart';

class SampleCaptureScreen extends StatefulWidget {
  final List<CameraDescription> cameras;
  
  const SampleCaptureScreen({
    Key? key,
    required this.cameras,
  }) : super(key: key);

  @override
  State<SampleCaptureScreen> createState() => _SampleCaptureScreenState();
}

class _SampleCaptureScreenState extends State<SampleCaptureScreen> {
  late CameraController _controller;
  bool _isInitialized = false;
  bool _isGoodQuality = true;
  String _sampleType = 'Blood Sample';

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }
  
  Future<void> _initializeCamera() async {
    final CameraController cameraController = CameraController(
      widget.cameras[0],
      ResolutionPreset.high,
      enableAudio: false,
    );

    await cameraController.initialize();
    
    if (!mounted) return;
    
    setState(() {
      _controller = cameraController;
      _isInitialized = true;
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const Scaffold(
        backgroundColor: Colors.black,
        body: Center(
          child: CircularProgressIndicator(
            color: Colors.white,
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Camera preview
          SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: CameraPreview(_controller),
          ),
          
          // Sample name
          Positioned(
            top: 50,
            left: 20,
            child: Text(
              _sampleType,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // Quality indicator
          Positioned(
            top: 45,
            right: 20,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.green,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                children: const [
                  Text(
                    'Quality: Good',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Focus frame
          Center(
            child: Container(
              width: 250,
              height: 250,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.white.withOpacity(0.5), width: 2),
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),

          // Sample image overlay (the AI-detected sample)
          Center(
            child: SizedBox(
              width: 250,
              height: 250,
              child: Image.asset(
                'assets/images/sample_overlay.jpg',
                fit: BoxFit.contain,
              ),
            ),
          ),

          // Focus tap instruction
          Positioned(
            bottom: 100,
            left: 0,
            right: 0,
            child: Text(
              'Tap anywhere to focus',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
                fontSize: 14,
              ),
            ),
          ),

          // Capture button
          Positioned(
            bottom: 30,
            left: 0,
            right: 0,
            child: Center(
              child: GestureDetector(
                onTap: _captureImage,
                child: Container(
                  width: 70,
                  height: 70,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 3),
                  ),
                  child: Center(
                    child: Container(
                      width: 60,
                      height: 60,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.red,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _captureImage() async {
    try {
      if (!_controller.value.isInitialized) {
        return;
      }

      // Display loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(
            color: Colors.white,
          ),
        ),
      );

      final image = await _controller.takePicture();
      
      if (!mounted) return;
      
      // Close loading dialog
      Navigator.pop(context);
      
      // Navigate to processing screen
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => AnalysisProgressScreen(imagePath: image.path),
        ),
      );
    } catch (e) {
      print('Error capturing image: $e');
    }
  }
}

// Placeholder for the analysis progress screen
class AnalysisProgressScreen extends StatelessWidget {
  final String imagePath;
  
  const AnalysisProgressScreen({
    Key? key,
    required this.imagePath,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    // To be replaced with the actual AnalysisProgressScreen
    return Scaffold(
      body: Center(
        child: Text('Processing image: $imagePath'),
      ),
    );
  }
}