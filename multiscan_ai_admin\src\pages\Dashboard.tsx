import { useEffect, useState } from "react";
import dashboardData from "../data/dashboard.json";
import type { DashboardData } from "../types/dashboard";
import DashboardChart from "../components/DashboardChart";
import WorldMap from "../components/WorldMap";
import { GoGraph } from "react-icons/go";
import { IoPeople } from "react-icons/io5";
import { FaRegCalendar, FaUserPlus } from "react-icons/fa";
import { TbCopyPlusFilled } from "react-icons/tb";
import { RiSettings3Fill } from "react-icons/ri";
import { BsExclamationCircle } from "react-icons/bs";
import { IoNotificationsOutline } from "react-icons/io5";
import { IoIosArrowDown } from "react-icons/io";
import { PiFilePlusFill } from "react-icons/pi";
import { FaPersonCirclePlus, FaSquarePlus } from "react-icons/fa6";
import { NavLink } from "react-router-dom";
import { IoIosSearch } from "react-icons/io";

const Dashboard = () => {
  const [data, setData] = useState<DashboardData | null>(null);

  useEffect(() => {
    setData(dashboardData as DashboardData);
  }, []);

  if (!data) return <div>Loading...</div>;

  return (
    <div className="flex bg-gray-50 h-screen overflow-hidden">
      {/* Sidebar */}
      <aside className="w-64 bg-white flex flex-col h-screen sticky top-0 overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center space-x-3">
            <img
              src="multiscan_logo.png"
              alt="MultiScan AI Logo"
              className="h-10 w-10"
            />
            <span className="text-2xl font-bold text-gray-900">
              MultiScan AI
            </span>
          </div>
        </div>
        <div className="p-6 flex-1">
          <nav className="space-y-4">
            <NavLink
              to="/dashboard"
              className={({ isActive }) =>
                `flex items-center space-x-2 p-2 rounded cursor-pointer ${
                  isActive
                    ? "bg-blue-100 text-blue-600"
                    : "hover:bg-gray-100 text-gray-700"
                }`
              }
            >
              <GoGraph className="size-5" />
              <span className="font-semibold">Dashboard</span>
            </NavLink>
            <NavLink
              to="/patients"
              className={({ isActive }) =>
                `flex items-center space-x-2 p-2 rounded cursor-pointer ${
                  isActive
                    ? "bg-blue-100 text-blue-600"
                    : "hover:bg-gray-100 text-gray-700"
                }`
              }
            >
              <IoPeople className="size-5" />
              <span>Patients</span>
            </NavLink>
            <NavLink
              to="/analysis"
              className={({ isActive }) =>
                `flex items-center space-x-2 p-2 rounded cursor-pointer ${
                  isActive
                    ? "bg-blue-100 text-blue-600"
                    : "hover:bg-gray-100 text-gray-700"
                }`
              }
            >
              <FaRegCalendar className="size-5" />
              <span>Analysis</span>
            </NavLink>
            <NavLink
              to="/medical-records"
              className={({ isActive }) =>
                `flex items-center space-x-2 p-2 rounded cursor-pointer ${
                  isActive
                    ? "bg-blue-100 text-blue-600"
                    : "hover:bg-gray-100 text-gray-700"
                }`
              }
            >
              <TbCopyPlusFilled className="size-5" />
              <span>Medical Records</span>
            </NavLink>
            <NavLink
              to="/settings"
              className={({ isActive }) =>
                `flex items-center space-x-2 p-2 rounded cursor-pointer ${
                  isActive
                    ? "bg-blue-100 text-blue-600"
                    : "hover:bg-gray-100 text-gray-700"
                }`
              }
            >
              <RiSettings3Fill className="size-5" />
              <span>Settings</span>
            </NavLink>
          </nav>
        </div>
        <div className="p-6 space-y-4">
          <div className="flex items-center space-x-2 p-2 text-gray-500 hover:bg-gray-100 rounded cursor-pointer">
            <BsExclamationCircle className="size-5" />
            <span className="font-medium">Help Centre</span>
          </div>
          <button className="w-full py-2 cursor-pointer bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            Log Out
          </button>
        </div>
      </aside>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Nav Bar */}
        <div className="flex bg-white p-4 h-16 sticky top-0 z-10">
          <div className="flex items-center border border-gray-300 rounded-lg px-3 py-1 focus-within:ring-2 focus-within:ring-blue-500 mr-6 flex-grow">
            <IoIosSearch className="text-gray-400 mr-2 size-6" />
            <input
              type="text"
              placeholder="Search for anything..."
              className="outline-none w-full"
            />
          </div>
          <div className="flex items-center space-x-4 mr-4">
            <IoNotificationsOutline className="size-5 text-gray-600 cursor-pointer" />

            <div className="flex items-center ml-autp space-x-2">
              <img
                src="avatar.png"
                alt="User Avatar"
                className="w-10 h-10 rounded-full"
              />
              <span className="text-gray-700">Flore</span>
            </div>
            <IoIosArrowDown className="text-gray-600 size-4 ml-auto cursor-pointer" />
          </div>
        </div>

        <main className="flex-1 p-8 overflow-y-auto">
          <h2 className="text-3xl font-bold mb-8 text-gray-900">
            Welcome Back, Flore!
          </h2>

          <div className="grid grid-cols-4 gap-6 mb-8">
            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
              <div className="flex justify-between">
                <div>
                  <div className="text-gray-500 text-sm">Total Patients</div>
                  <div className="text-2xl font-bold text-gray-900">
                    {data.stats.totalPatients.toLocaleString()}
                  </div>
                  <div className="text-green-500 text-sm">
                    +{data.stats.totalPatientsChange}% from last month
                  </div>
                </div>
                <FaPersonCirclePlus className="size-6 text-blue-500" />
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
              <div className="flex justify-between items-start">
                <div>
                  <div className="text-gray-500 text-sm">Today's Reports</div>
                  <div className="text-2xl font-bold text-gray-900">
                    {data.stats.todaysReports}
                  </div>
                  <div className="text-purple-500 text-sm">
                    {data.stats.urgentReports} urgent cases
                  </div>
                </div>
                <PiFilePlusFill className="size-6 text-purple-600" />
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
              <div className="flex justify-between items-start">
                <div>
                  <div className="text-gray-500 text-sm">New Patients</div>
                  <div className="text-2xl font-bold text-gray-900">
                    {data.stats.newPatients}
                  </div>
                  <div className="text-green-500 text-sm">
                    +{data.stats.newPatientsChange}% this week
                  </div>
                </div>
                <FaUserPlus className="size-6 text-green-500" />
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
              <div className="flex justify-between items-start">
                <div>
                  <div className="text-gray-500 text-sm">Critical Cases</div>
                  <div className="text-2xl font-bold text-gray-900">
                    {data.stats.criticalCases}
                  </div>
                  <div className="text-red-500 text-sm">
                    {data.stats.criticalCasesUrgent} require immediate attention
                  </div>
                </div>
                <FaSquarePlus className="size-6 text-red-500" />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow-md border border-gray-100 h-80">
              <div className="flex justify-between items-start p-6">
                <span className="text-gray-700 font-semibold">
                  Misdiagnosis cases
                </span>
                <select className="border border-gray-300 rounded-lg p-1 text-sm">
                  <option>Monthly</option>
                </select>
              </div>
              <div className="p-6 pt-0">
                <DashboardChart data={data.misdiagnosisCases} />
              </div>
            </div>
            <div className="bg-white p-1 rounded-lg shadow-md border border-gray-100 h-80">
              <WorldMap regions={data.misdiagnosisRegions} />
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
            <div className="flex items-center mb-4 mx-4">
              <div className="font-semibold text-gray-900">Recent Patients</div>
              <div className="flex ml-auto">
                <div className="flex items-center border border-gray-300 rounded-lg px-3 py-1 focus-within:ring-2 focus-within:ring-blue-500 ml-auto mr-4">
                  <IoIosSearch className="text-gray-400 mr-2 size-6" />
                  <input
                    type="text"
                    placeholder="Search patients..."
                    className="outline-none w-full"
                  />
                </div>
                <button className="px-4 py-2 w-38 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                  Search
                </button>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full text-left">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="py-2 px-4 font-semibold text-gray-700">
                      Patient ID
                    </th>
                    <th className="py-2 px-4 font-semibold text-gray-700">
                      Patient
                    </th>
                    <th className="py-2 px-4 font-semibold text-gray-700">
                      Age
                    </th>
                    <th className="py-2 px-4 font-semibold text-gray-700">
                      Contact
                    </th>
                    <th className="py-2 px-4 font-semibold text-gray-700">
                      Condition
                    </th>
                    <th className="py-2 px-4 font-semibold text-gray-700">
                      Last Visit
                    </th>
                    <th className="py-2 px-4 font-semibold text-gray-700">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {data.recentPatients.map((p) => (
                    <tr
                      key={p.id}
                      className="even:bg-gray-50 hover:bg-gray-100"
                    >
                      <td className="py-2 px-4">{p.id}</td>
                      <td className="py-2 px-4 flex items-center space-x-2">
                        <img
                          src={`/${p.avatar}`}
                          alt={p.name}
                          className="w-8 h-8 rounded-full"
                        />
                        <span>
                          {p.name}{" "}
                          <span className="text-gray-500 text-sm">
                            #{p.id.slice(-4)}
                          </span>
                        </span>
                      </td>
                      <td className="py-2 px-4">{p.age}</td>
                      <td className="py-2 px-4">
                        {p.contact}
                        <br />
                        <span className="text-xs text-gray-400">{p.email}</span>
                      </td>
                      <td className="py-2 px-4">
                        <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs">
                          {p.condition}
                        </span>
                      </td>
                      <td className="py-2 px-4">{p.lastVisit}</td>
                      <td className="py-2 px-4 text-blue-600 cursor-pointer">
                        View Details
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Dashboard;
