class LoginModel {
  String? emailOrPhone;
  String? password;
  bool rememberMe;

  LoginModel({this.emailOrPhone, this.password, this.rememberMe = false});

  // Method to validate the form
  String? validateEmailOrPhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter email or phone';
    }
    return null;
  }

  String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter password';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  // Convert to JSON for API calls
  Map<String, dynamic> toJson() {
    return {
      'emailOrPhone': emailOrPhone,
      'password': password,
      'rememberMe': rememberMe,
    };
  }
}
