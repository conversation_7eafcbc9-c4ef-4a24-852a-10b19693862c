import 'package:flutter/material.dart';
import '../models/login_model.dart';

class LoginViewModel {
  final LoginModel _model = LoginModel();
  final formKey = GlobalKey<FormState>();

  // Getters for form fields
  String? get emailOrPhone => _model.emailOrPhone;
  String? get password => _model.password;
  bool get rememberMe => _model.rememberMe;

  // Update methods
  void updateEmailOrPhone(String value) {
    _model.emailOrPhone = value;
  }

  void updatePassword(String value) {
    _model.password = value;
  }

  void toggleRememberMe(bool value) {
    _model.rememberMe = value;
  }

  // Form validation
  String? validateEmailOrPhone(String? value) {
    return _model.validateEmailOrPhone(value);
  }

  String? validatePassword(String? value) {
    return _model.validatePassword(value);
  }

  // Login method
  Future<void> login(BuildContext context) async {
    if (formKey.currentState?.validate() ?? false) {
      formKey.currentState?.save();

      // Here you would typically call an authentication service
      try {
        // Simulate API call
        await Future.delayed(const Duration(seconds: 1));

        // If login is successful, navigate to home screen
        Navigator.pushReplacementNamed(context, '/home');
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Login failed: ${e.toString()}')),
        );
      }
    }
  }

  // Navigation methods
  void navigateToForgotPassword(BuildContext context) {
    Navigator.pushNamed(context, '/forgot-password');
  }

  void navigateToSignUp(BuildContext context) {
    Navigator.pushNamed(context, '/signup');
  }
}
