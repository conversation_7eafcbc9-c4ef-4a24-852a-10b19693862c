import 'package:flutter/material.dart';
import '../views/splash_screen.dart';
import '../views/welcome_screen.dart';
import '../views/login_screen.dart';
import '../views/signup_screen.dart';
import '../views/home_screen.dart';
import '../views/transition_screen.dart';

class AppRoutes {
  static const String splash = '/splash';
  static const String welcome = '/';
  static const String login = '/login';
  static const String signup = '/signup';
  static const String home = '/home';
  static const String transition = '/transition';
  // Add more routes here as you create new screens
  // static const String forgotPassword = '/forgot-password';

  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case splash:
        return MaterialPageRoute(builder: (_) => SplashScreen());
      case welcome:
        return MaterialPageRoute(builder: (_) => const WelcomeScreen());
      case login:
        return MaterialPageRoute(builder: (_) => LoginScreen());
      case signup:
        return MaterialPageRoute(builder: (_) => SignupScreen());
      case home:
        return MaterialPageRoute(builder: (_) => HomeScreen());
      case transition:
        return MaterialPageRoute(builder: (_) => const TransitionScreen());
      // Add more cases for other routes
      default:
        return MaterialPageRoute(
          builder:
              (_) => Scaffold(
                body: Center(
                  child: Text('No route defined for ${settings.name}'),
                ),
              ),
        );
    }
  }

  static Map<String, WidgetBuilder> get routes {
    return {
      splash: (context) => SplashScreen(),
      welcome: (context) => const WelcomeScreen(),
      login: (context) => LoginScreen(),
      signup: (context) => SignupScreen(),
      home: (context) => HomeScreen(),
      transition: (context) => const TransitionScreen(),
      // Add other routes here
    };
  }
}
