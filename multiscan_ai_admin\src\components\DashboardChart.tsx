import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from "recharts";
import type { MisdiagnosisCase } from "../types/dashboard";

interface DashboardChartProps {
  data: MisdiagnosisCase[];
}

const DashboardChart = ({ data }: DashboardChartProps) => (
  <ResponsiveContainer width="100%" height={220}>
    <LineChart
      data={data}
      margin={{ top: 20, right: 10, left: -10, bottom: 0 }}
    >
      <CartesianGrid vertical={false} stroke="#E5E7EB" strokeDasharray="0" />
      <XAxis dataKey="month" stroke="#6B7280" />
      <YAxis stroke="#6B7280" />
      <Tooltip
        contentStyle={{
          backgroundColor: "#fff",
          borderRadius: "8px",
          border: "1px solid #E5E7EB",
        }}
        labelStyle={{ color: "#1F2937" }}
      />
      <Legend
        verticalAlign="top"
        height={36}
        iconType="circle"
        formatter={(value, entry) => (
          <span style={{ color: "#1F2937", marginLeft: "5px" }}>{value}</span>
        )}
      />
      <Line
        type="monotone"
        dataKey="offline"
        stroke="#4B6BFB"
        name="Offline analytics"
        strokeWidth={3}
        dot={false}
        activeDot={{ r: 4, fill: "#4B6BFB", stroke: "#fff", strokeWidth: 2 }}
      />
      <Line
        type="monotone"
        dataKey="online"
        stroke="#F4A261"
        name="Online analytics"
        strokeWidth={3}
        dot={false}
        activeDot={{ r: 4, fill: "#F4A261", stroke: "#fff", strokeWidth: 2 }}
      />
    </LineChart>
  </ResponsiveContainer>
);

export default DashboardChart;
